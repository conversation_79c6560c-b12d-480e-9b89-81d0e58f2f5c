install_packages = False
sweeping = False
sweep_id = None

# for gradient
if install_packages:
    %pip install ipywidgets
    %pip install pandas
    %pip install onnx
    %pip install onnxruntime

# Standard utils
import numpy as np
import pandas as pd
import os 
from pathlib import Path
import math
import sys
import traceback
from tqdm.notebook import tqdm

# XLA for TPU
# assert os.environ['COLAB_TPU_ADDR'], 'Make sure to select TPU from Edit > Notebook settings > Hardware accelerator'
# !pip install cloud-tpu-client==0.10 https://storage.googleapis.com/tpu-pytorch/wheels/torch_xla-1.9-cp37-cp37m-linux_x86_64.whl
# import torch_xla
# import torch_xla.core.xla_model as xm

# NN tools
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
if install_packages:
    %pip install joblib
import joblib
import gc
gc.collect()
torch.cuda.empty_cache()

# Plotting things
if install_packages:
    %pip install plotly
import plotly.express as px
import matplotlib.pyplot as plt
# %matplotlib widget 
#inline #notebook #widget

# Ensure deterministic behavior
torch.manual_seed(33)
np.random.seed(33)

%%capture
if install_packages:
    %pip install wandb --upgrade
import wandb
import utils

os.environ["WANDB_NOTEBOOK_NAME"] = "fp_learner.ipynb"
wandb.login()

# set up TPU
# device = xm.xla_device()

if torch.cuda.is_available():
    device = torch.device('cuda')
    gpu = True
# elif torch.backends.mps.is_available():
#     device = torch.device('mps')
#     gpu = True
else:                                                  
    device = torch.device('cpu')
    gpu = False
    
torch.set_default_dtype(torch.float32)
torch.set_default_device(device)
print("Using", device) #sanity check

# Import configuration from fp_config.py\n",
from fp_config import training, run_name, nn_config, learner_sweep_config, sweeping
# For backward compatibility
sweep_config = learner_sweep_config

# # Function to calculate reconstruction error with autoencoder
# def reconstruction_error(model, data):
#     model.eval()
#     with torch.no_grad():
#         data = torch.tensor(data, dtype=torch.float32)
#         outputs = model(data)
#         mse = torch.mean((data - outputs) ** 2) #dim=1
#     return mse.numpy()

# train_loader = None
# threshold = None

def training_pipeline(hyperparameters=None):
    """
    Run the model training pipeline
    :param hyperparameters - a dictionary of the model / training configuration
    return - resultant model
    """

    torch.cuda.empty_cache()

        # dataset.plate_serial, dataset.plate_model_name, dataset.plate_model_number, dataset.loadcell_model, dataset.truth_source, dataset.capture_env, dataset.daq
        # V03, Vader, 6.01, TAS501, MichSci, Rig, DAQ 0.1
        # W01, Watson, 5.44, TAS501S, AMTI, Lab, LockLab

    if hyperparameters is not None:
        if  hyperparameters['type'] == "PINN":
            run_tags = [hyperparameters['type'], hyperparameters['physics_informed_type'], hyperparameters['metric']]
        else:
            run_tags = [hyperparameters['type'], hyperparameters['metric']]
        notes = f"Standard normalisation (input and output); {hyperparameters['layers'].count} hidden layers with dropout of {hyperparameters['hidden_dropout']}, and/or BN; {hyperparameters['target_cols']};"
    else:
        if sweeping:
            run_tags = [sweep_config['parameters']['type']['value'], sweep_config['parameters']['metric']['value']]
            notes = "Standard normalisation (input and output); Sweeping - see config."

    # Tell wandb to get started
    with wandb.init(
        project="force-plate",
        notes=notes,
        tags=run_tags,
        config=hyperparameters,
        save_code=True) as run:   

        global run_name
        global train_loader
        global threshold
        # global config
        run_name = run.name  

        # Access all hyperparams through wandb.config, so logging matches execution
        config = wandb.config
        
        # Add dataset metadata to wandb run config
        raw_data_artifact = run.use_artifact(config.artifact_name)
        if 'dataset_id' in raw_data_artifact.metadata: run.tags = run.tags + (raw_data_artifact.metadata['dataset_id'],)
        if 'capture_env' in raw_data_artifact.metadata: run.tags = run.tags + (raw_data_artifact.metadata['capture_env'],)
        if 'num_loadcells' in raw_data_artifact.metadata: run.tags = run.tags + (f"{raw_data_artifact.metadata['num_loadcells']}-sensor",)

        run.config.update(raw_data_artifact.metadata)
        if not sweeping:
            run.config.update({'layercount': len(config.layers)})
            run.config.update({'layers': config.layers[:config.layercount]})
        # else:
        #     print(config)
        #     run.config.update({'layers': config.layers[:config.layercount]})

        # Make the model, data, and optimization problem
        model, train_loader, test_loader, val_loader, test_CoP, test_forces, criterion, optimizer, scheduler = make(config, raw_data_artifact, device)


# Autoencoder model to be used for anomaly detection / out of distribution detection
        # # import torch
        # # import torch.nn as nn
        # import torch.optim as optim
        # # from torch.utils.data import DataLoader, TensorDataset

        # # Assuming your data is stored in numpy arrays X_train and X_val
        # # X_train = torch.tensor(X_train, dtype=torch.float32)
        # # X_val = torch.tensor(X_val, dtype=torch.float32)

        # # # Create DataLoader
        # # train_loader = DataLoader(TensorDataset(X_train, X_train), batch_size=256, shuffle=True)
        # # val_loader = DataLoader(TensorDataset(X_val, X_val), batch_size=256, shuffle=False)

        # # Define the Autoencoder Model
        # class Autoencoder(nn.Module):
        #     def __init__(self, input_dim, encoding_dim):
        #         super(Autoencoder, self).__init__()
        #         self.encoder = nn.Sequential(
        #             nn.Linear(input_dim, encoding_dim),
        #             nn.ReLU(True),
        #             nn.Linear(encoding_dim, encoding_dim), # // 2
        #             nn.ReLU(True),
        #         )
        #         self.decoder = nn.Sequential(
        #             nn.Linear(encoding_dim, encoding_dim),
        #             nn.ReLU(True),
        #             nn.Linear(encoding_dim, input_dim),
        #             nn.Sigmoid()
        #         )

        #     def forward(self, x):
        #         x = self.encoder(x)
        #         x = self.decoder(x)
        #         return x

        # input_dim = train_loader.dataset.x.shape[1]
        # encoding_dim = 5

        # model = Autoencoder(input_dim, encoding_dim)
        # criterion = nn.MSELoss()
        # optimizer = optim.Adam(model.parameters(), lr=0.01)
        # steps = len(train_loader)
        # num_epochs = 20
        # scheduler = torch.optim.lr_scheduler.OneCycleLR(optimizer, max_lr=0.01, epochs=num_epochs, steps_per_epoch=steps)

        # # Training the Autoencoder
        # for epoch in range(num_epochs):
        #     model.train()
        #     train_loss = 0
        #     for data in train_loader:
        #         inputs, _ = data
        #         optimizer.zero_grad()
        #         outputs = model(inputs)
        #         loss = criterion(outputs, inputs)
        #         loss.backward()
        #         optimizer.step()
        #         scheduler.step()
        #         train_loss += loss.item()
            
        #     model.eval()
        #     val_loss = 0
        #     with torch.no_grad():
        #         for data in val_loader:
        #             inputs, _ = data
        #             outputs = model(inputs)
        #             loss = criterion(outputs, inputs)
        #             val_loss += loss.item()

        #     print(f'Epoch [{epoch+1}/{num_epochs}], Train Loss: {train_loss/len(train_loader):.4f}, Val Loss: {val_loss/len(val_loader):.4f}')

        # # Calculate reconstruction error for validation set
        # val_error = reconstruction_error(model, val_loader.dataset.x)
        # print(f'Validation Error: {val_error:.4f}')
        # threshold = np.percentile(val_error, 99)  # Example threshold setting



        # ...and use them to train the model
        train_loss, val_loss = None, None
        try:
            base_save_path = os.path.join(wandb.run.dir, run_name)
            train_loss, val_loss = utils.train(model, train_loader, val_loader, criterion, optimizer, scheduler, config, device)
            global x_scaler, y_scaler
            num_inputs = utils.get_num_inputs(model)
            print(f"Saving model with {num_inputs} inputs")
            if config.scale_in_model:
                model = utils.combine_model_and_scalers(model, x_scaler, y_scaler, num_inputs, len(config.target_cols))
            print("Combined model before saving: ", model)     
            utils.save_model(model, x_scaler, y_scaler, num_inputs, config.target_cols, base_save_path, sequence_length=config.sequence_length)
            utils.quantize_model_and_save(model, x_scaler, y_scaler, config.target_cols, base_save_path, train_loader, config.scale_in_model)
        except Exception as e:
            # exit gracefully, so wandb logs the problem
            print(traceback.print_exc(), file=sys.stderr)
            print(type(e), e)
            raise(e)
        finally:
            # ...and test its final performance, passing in CoPs for our plotting pleasure
            print("Starting testing")
            test_loss = test(model, test_loader, test_CoP, test_forces, criterion, config, base_save_path)
            if train_loss is not None and val_loss is not None:
                combined_val_test_loss = val_loss + test_loss
                wandb.log({"combined_val_test_loss": combined_val_test_loss})
            

    return model, test_loader, test_CoP, criterion, config

def testing_pipeline(model_path):
    # Make the model, data, and optimization problem
    with wandb.init(config=hyperparameters) as run:
        config = wandb.config
        raw_data_artifact = run.use_artifact(config.artifact_name)
        model, train_loader, test_loader, val_loader, test_CoP, test_forces, criterion = recall(model_path, raw_data_artifact)
        print(model)

    # ...and test its performance
    test(model, test_loader, test_CoP, test_forces, criterion, config)

# def forward_hook(m, x, nn_output):
#     '''Executes after forward pass of nn. Must be registered with 
#         forward_hook_handle = m.register_forward_hook(forward_hook)'''
#     #  computation
#     physics = calc_physics(x[0]) # inputs are wrapped in a tuple, hence the [0] call
#     return physics + nn_output

class Autoencoder(nn.Module):
    def __init__(self, input_dim, encoding_dim):
        super(Autoencoder, self).__init__()
        # Encoder
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 6),
            nn.ReLU(True),
            nn.Linear(6, encoding_dim),
            nn.ReLU(True)
        )
        # Decoder
        self.decoder = nn.Sequential(
            nn.Linear(encoding_dim, 6),
            nn.ReLU(True),
            nn.Linear(6, input_dim),
            nn.Sigmoid()
        )

    def forward(self, x):
        x = self.encoder(x)
        x = self.decoder(x)
        return x
    
    def get_latent_representation(self, x):
        return self.encoder(x)

def train_autoencoder(model, criterion, optimizer, train_loader, num_epochs):
    for epoch in range(num_epochs):
        for data in train_loader:
            inputs, _ = data
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, inputs)
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        print(f'Autoencoder: epoch [{epoch+1}/{num_epochs}], Loss: {loss.item():.4f}')

    torch.save(model.state_dict(), 'autoencoder.pth')

def get_latent_space(model, data_loader):
    model.eval()
    latent_space = []
    with torch.no_grad():
        for data in data_loader:
            inputs, _ = data
            encoded = model.encoder(inputs)
            latent_space.append(encoded)
    return torch.cat(latent_space).numpy()

if not sweeping:
    model_type = nn_config['type']
    learn_residual = nn_config['residual_learner']
else:
    model_type = sweep_config['parameters']['type']['value']
    learn_residual = sweep_config['parameters']['residual_learner']['value']

if model_type == "LSTM" and learn_residual: # i.e. we're learning with sequenced input, flatten for linear model
    class LinearModel(nn.Module):
        def __init__(self, in_features=4, out_features=4, sequence_len=3):
            super(LinearModel, self).__init__()
            self.in_features = in_features
            self.out_features = out_features
            self.sequence_len = sequence_len
            self.linear = nn.Linear(sequence_len * in_features, out_features)
        
        def forward(self, x):
            is_batched = len(x.shape) == 3
            batch_size = x.size(0) if is_batched else 1  # If not batched, set batch_size to 1
            # Reshape x to (batch_size, sequence_len * in_features) to apply linear model
            x = x.view(batch_size, self.sequence_len * self.in_features)
            output = self.linear(x)
            return output
else:
    class LinearModel(nn.Module):
        def __init__(self, in_features=4, out_features=4):
            super(LinearModel, self).__init__()
            self.linear = nn.Linear(in_features, out_features)
        
        def forward(self, x):
            return self.linear(x)
    
def train_linear_model(model, criterion, optimizer, train_loader, num_epochs):
    for epoch in range(num_epochs):
        for data in train_loader:
            inputs, targets = data
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        print(f'Linear Model: epoch [{epoch+1}/{num_epochs}], Loss: {loss.item():.4f}')
    
    return model
    
class ResidualModel(nn.Module):
    def __init__(self, linear_model, residual_nn):
        super(ResidualModel, self).__init__()
        self.linear_model = linear_model
        self.residual_nn = residual_nn

        for param in self.linear_model.parameters():
            param.requires_grad = False
    
    def forward(self, x):
        linear_output = self.linear_model(x)
        residual_output = self.residual_nn(x)
        return linear_output + residual_output

if model_type == "FEANN":
    class Model(nn.Module):
        def __init__(self, base, layers, in_features=4, out_features=4, p_input=0.2, p_hidden=0.5, batch_norm=False):
            super().__init__()
            layerlist = [] 
            self.pretrained_model = base ##
            
            # Input layer (4 features - 4 "voltages") --> hidden layers--> output layer (Fx, Fy, Fz)
            for i, neurons in enumerate(layers):
                layerlist.append(nn.Linear(in_features, neurons)) 

                if batch_norm:
                    layerlist.append(nn.BatchNorm1d(neurons))

                layerlist.append(nn.ReLU(inplace=True))

                if (i == 0): # apply smaller dropout to input layer
                    if p_input != 0: layerlist.append(nn.Dropout(p_input))
                else: 
                    if p_hidden != 0: layerlist.append(nn.Dropout(p_hidden))
                    
                in_features = neurons
            
            if len(layers) > 0:
                layerlist.append(nn.Linear(layers[-1], out_features))
            else: 
                layerlist.append(nn.Linear(in_features, out_features)) # in case we're just re-adding an output layer
            self.layers = nn.Sequential(*layerlist)
            
        def forward(self, x):
            if self.pretrained_model: x = self.pretrained_model(x)
            x = self.layers(x)
            return x
elif model_type == "KAN":
    from efficient_kan import KAN
    class Model(KAN):
        def __init__(self, layers, in_features=4, out_features=3, p_input=0.2, p_hidden=0.5, batch_norm=False, config=None):
            super().__init__([in_features, *layers, out_features])
elif model_type == "LSTM":
    class Model(nn.Module):
        def __init__(self, layers=[10, 20], in_features=4, out_features=3, config=None):
            super(Model, self).__init__()
            
            self.layers = nn.ModuleList()
            self.hidden_sizes = layers
            self.config = config
            input_size = in_features

            for i, hidden_size in enumerate(layers):
                self.layers.append(nn.LSTM(input_size, hidden_size, batch_first=True))
                input_size = hidden_size
            # if self.config and self.config.get('use_linear_between', False):
                # self.layers.append(nn.Linear(input_size, input_size))
            
            self.output_layer = nn.Linear(input_size, out_features)
            self.hidden_states = None
        
        def forward(self, x):
            is_batched = len(x.shape) == 3  # Check if input is batched
            batch_size = x.size(0) if is_batched else 1  # If not batched, set batch_size to 1
            
            out = x
            for layer in self.layers:
                if isinstance(layer, nn.LSTM):
                    h_0 = torch.zeros(1, batch_size, layer.hidden_size).to(x.device)
                    c_0 = torch.zeros(1, batch_size, layer.hidden_size).to(x.device)
                    
                    if not is_batched:
                        out = out.unsqueeze(0)  # Add batch dimension if not batched
                    
                    out, _ = layer(out, (h_0, c_0))
                    
                    if not is_batched:
                        out = out.squeeze(0)  # Remove batch dimension if not batched
                else:
                    out = layer(out)
            
            out = self.output_layer(out[:, -1, :] if is_batched else out[-1, :])  # Handle last time step output correctly
            return out
        
        # # if saving state between batches, need to reset hidden states between epochs
        # def init_hidden(self, batch_size):
        #     self.hidden_states = [
        #         (torch.zeros(1, batch_size, hidden_size),
        #         torch.zeros(1, batch_size, hidden_size))
        #         for hidden_size in self.hidden_sizes
        #     ]

        # def forward(self, x):
        #     batch_size = x.size(0)
        #     if self.hidden_states is None or self.hidden_states[0][0].size(1) != batch_size:
        #         self.init_hidden(batch_size)
            
        #     out = x
        #     new_hidden_states = []
        #     for i, layer in enumerate(self.layers):
        #         if isinstance(layer, nn.LSTM):
        #             out, (hn, cn) = layer(out, self.hidden_states[i])
        #             new_hidden_states.append((hn, cn))
        #         elif isinstance(layer, nn.Linear):
        #             out = layer(out)
            
        #     self.hidden_states = new_hidden_states
        #     out = self.output_layer(out[:, -1, :])  # Only take the last time step output for final prediction # self.linear(lstm_out.view(len(input_seq), -1))
        #     return out
        
else:
    class Model(nn.Module):
        def __init__(self, layers, in_features=4, out_features=3, p_input=0.2, p_hidden=0.5, batch_norm=False, config=None):
            super().__init__()
            layerlist = []
            self.config = config
            self.use_autoencoder = config.use_autoencoder
            self.autoencoder = Autoencoder(in_features, config.autoencoder_encoding_dim) if self.use_autoencoder else None

            if self.use_autoencoder:
                self.autoencoder.load_state_dict(torch.load('autoencoder.pth'))
                in_features = config.autoencoder_encoding_dim  # Update in_features to the encoding dimension if using autoencoder
            
            for i, neurons in enumerate(layers):
                layer = nn.Linear(in_features, neurons)
                layerlist.append(layer)
                
                if batch_norm:
                    layerlist.append(nn.BatchNorm1d(neurons))
                
                layerlist.append(nn.ReLU(inplace=True))
                
                if i == 0:
                    if p_input != 0: 
                        layerlist.append(nn.Dropout(p_input))
                else:
                    if p_hidden != 0: 
                        layerlist.append(nn.Dropout(p_hidden))
                
                in_features = neurons
            
            layerlist.append(nn.Linear(layers[-1], out_features))
            
            self.layers = nn.Sequential(*layerlist)

            # Embed constants and parameters as buffers
            if model_type == "PINN":
                self.register_buffer('counts_to_forces', self.calculate_counts_to_forces(config))

        def calculate_counts_to_forces(self, config):
            EXCITATION = 12
            counts_to_V_conversion = config.daq_ref_v / (config.daq_max_normal_reading * torch.tensor(config.daq_gains, dtype=torch.float32))
            v_to_kg_conversion = torch.tensor(config.loadcell_ranges_kg, dtype=torch.float32) / (torch.tensor(config.loadcell_sensitivities_vpv, dtype=torch.float32) * EXCITATION)
            return counts_to_V_conversion * v_to_kg_conversion * 9.81

        def forward(self, x):
            if self.use_autoencoder:
                x = self.autoencoder.get_latent_representation(x)
            nn_output = self.layers(x)
            if model_type == "PINN":
                return self.calc_physics(x) + nn_output
            else:
                return nn_output

        def calc_physics(self, X):
            if X.shape[1] != 6:
                raise ValueError(f"Physics calculations require an input tensor with 6 columns, not {X.shape[1]}")
            
            # X_inv = x_scaler.inverse_transform(X.cpu().detach().numpy())
            # X_inv = torch.from_numpy(X_inv).type(torch.float32).to(X.device)
            # X = X_inv * self.counts_to_forces.to(X.device)
            
            # Fx = X[:, [4]] * 6 / 1
            # Fy = X[:, [2]] * 6 / 1
            # Fz = X[:, [0, 1, 3, 5]].sum(dim=1, keepdim=True) * 6 / 4
            # Cx = torch.zeros((X.shape[0], 1), device=X.device)
            # Cy = torch.zeros((X.shape[0], 1), device=X.device)
            
            # axes = {'Fx': Fx, 'Fy': Fy, 'Fz': Fz, 'Cx': Cx, 'Cy': Cy}
            # axes_list = [axes[key] for key in self.config.target_cols]
            # output_unscaled = torch.cat(axes_list, dim=1)
            # output_unscaled_np = output_unscaled.cpu().detach().numpy()
            
            # make example data
            # output_unscaled_np = np.ones((X.shape[0], 5))*500
            # out_scaled_np = y_scaler.transform(output_unscaled_np)
            # out_scaled = torch.from_numpy(out_scaled_np).type(torch.float32).to(X.device)


            X_inv = torch.tensor(x_scaler.inverse_transform(X.cpu().detach().numpy()), device=X.device).float()
            X = X_inv * self.counts_to_forces

            Fx = X[:, [4]] * 6 / 1
            Fy = X[:, [2]] * 6 / 1
            Fz = X[:, [0, 1, 3, 5]].sum(dim=1, keepdim=True) * 6 / 4
            # Cx = torch.zeros((X.shape[0], 1), device=X.device)
            # Cy = torch.zeros((X.shape[0], 1), device=X.device)

            # output_unscaled = torch.cat((Fx, Fy, Fz, Cx, Cy), dim=1)
            output_unscaled = torch.cat((Fx, Fy, Fz), dim=1)
            out_scaled = torch.tensor(y_scaler.transform(output_unscaled.cpu().detach().numpy()), device=X.device).float()

            return out_scaled

def test(model, test_loader, Y_CoPs, Y_forces, criterion, config, base_save_path):
    global x_scaler, y_scaler, Y_Fzs
    model.eval()
    model.to(device)
    test_losses = []
    all_Y_pred = []
    all_Y_true = []
    Y_CoPs = pd.DataFrame(Y_CoPs, index=None, columns=['Cx','Cy'])
    Y_forces = pd.DataFrame(Y_forces, index=None, columns=['Fx','Fy','Fz'])

    with torch.no_grad():
        for batch_X, batch_Y in test_loader:
            # Check if the input is 3D (batched sequence) or 2D (batched single frame)
            if batch_X.dim() == 3: # Batched sequence data
                batch_size, seq_len, num_features = batch_X.shape
                if config.scale_in_model: # if scaling is part of the model, we need to unscale it here
                    # Reshape batch_X to 2D for inverse scaling
                    batch_X_reshaped = batch_X.view(-1, num_features)
                    batch_X_reshaped = torch.Tensor(x_scaler.inverse_transform(batch_X_reshaped.cpu())).to(device)
                    batch_X = batch_X_reshaped.view(batch_size, seq_len, num_features)
            elif batch_X.dim() == 2: # Batched single frame data
                batch_size, num_features = batch_X.shape
                if config.scale_in_model: # if scaling is part of the model, we need to unscale it here
                    batch_X = torch.Tensor(x_scaler.inverse_transform(batch_X.cpu())).to(device)
            else:
                raise ValueError("Unexpected input shape: {}".format(batch_X.shape))
            
            batch_X = batch_X.to(device)
            batch_Y = batch_Y.to(device)

            # Forward pass
            Y_pred = model(batch_X)
            Y_pred_reshaped = Y_pred.view(-1, Y_pred.size(-1))

            # Calculate the loss with data scaled down as in training
            if config.scale_in_model:
                Y_pred_scaled = torch.Tensor(y_scaler.transform(Y_pred_reshaped.cpu())).to(device)
                Y_pred_scaled = Y_pred_scaled.view(batch_size, -1) if batch_X.dim() == 3 else Y_pred_scaled
            else:
                Y_pred_scaled = Y_pred

            loss = criterion(Y_pred_scaled.cpu(), batch_Y.cpu(), batch_X.cpu()).detach()

            test_losses.append(loss.item())

            # Rescale the output data back if not done in-model
            if not config.scale_in_model: 
                Y_pred_reshaped = torch.Tensor(y_scaler.inverse_transform(Y_pred_reshaped.cpu())).to(device) # pred
                Y_pred = Y_pred_reshaped.view(batch_size, -1)
                
            batch_Y_reshaped = batch_Y.view(-1, batch_Y.size(-1))
            batch_Y_reshaped = torch.Tensor(y_scaler.inverse_transform(batch_Y_reshaped.cpu())).to(device)
            batch_Y = batch_Y_reshaped.view(batch_size, -1)

            all_Y_pred.append(Y_pred.cpu())
            all_Y_true.append(batch_Y.cpu())

    # Concatenate all predictions and true values
    all_Y_pred = torch.cat(all_Y_pred)
    all_Y_true = torch.cat(all_Y_true)

    # Calculate mean test loss
    mean_test_loss = sum(test_losses) / len(test_losses)
    
    # Log test loss
    if training: wandb.log({"test_loss": mean_test_loss})
    print(f"Test loss: {mean_test_loss:.8f}")

    pred_cols = []
    for col in config.target_cols:
        pred_cols.append(col + "_pred")

    test_truth = pd.DataFrame(all_Y_true, index=None, columns=config.target_cols)
    test_pred = pd.DataFrame(all_Y_pred, index=None, columns=pred_cols)
    test_set_df = pd.concat([test_truth, test_pred], axis=1)

    # get missing truth columns and add to dataframe
    missing_f_cols = list(set(['Fx', 'Fy', 'Fz']) - set(config.target_cols))
    missing_c_cols = list(set(['Cx', 'Cy']) - set(config.target_cols))
    if len(missing_f_cols) > 0:
        test_set_df[missing_f_cols] = Y_forces[missing_f_cols]
    if len(missing_c_cols) > 0:
        test_set_df[missing_c_cols] = Y_CoPs[missing_c_cols]
        
    # exclude data outside specified range limits from test set
    if config.limit_test_range:
        test_set_df = test_set_df.loc[abs(test_set_df['Fx']) < config.range_limits[0]]
        test_set_df = test_set_df.loc[abs(test_set_df['Fy']) < config.range_limits[1]]
        test_set_df = test_set_df.loc[abs(test_set_df['Fz']) < config.range_limits[2]]

        all_Y_pred = test_set_df[pred_cols].to_numpy()
        all_Y_true = test_set_df[config.target_cols].to_numpy()
        Y_CoPs = test_set_df[['Cx','Cy']]
        Y_forces = test_set_df[['Fx','Fy','Fz']]

    # if ./outputs doesn't exist, create it
    global run_name
    if not os.path.exists("./outputs"):
        os.makedirs("./outputs")
    test_set_df.to_csv(f"./outputs/{run_name}_test_set_pred.csv")
    print(f"Writing test set predictions to ./outputs/{run_name}_test_set_pred.csv")

    plate_size = (config.plate_dim_x, config.plate_dim_y)

    utils.analyse_model_perf(all_Y_pred, all_Y_true, Y_CoPs, Y_forces, config.target_cols, plate_size=(plate_size), log_to_wandb=training, plot=not sweeping, threshold=config.fz_analysis_threshold_value, base_save_path=base_save_path)
    return mean_test_loss



x_scaler = StandardScaler()
y_scaler = StandardScaler()

# Read in trials one by one and concatenate into one big dataframe
def get_data(config, raw_data_artifact:wandb.Artifact, device):
    print("Getting data") 
    
    # if need be, download the artifact
    data_dir = 'artifacts/' + raw_data_artifact.name
    if not os.path.isdir(data_dir): # check if data directory has already been downloaded     
        data_dir = raw_data_artifact.download()

    if 'capture_env' not in config:
        config.capture_env = None
    if 'num_loadcells' not in config:
        config.num_loadcells = 8
    if 'plate_dim_x' not in config:
        config.plate_dim_x = 500
    if 'plate_dim_y' not in config:
        config.plate_dim_y = 500
    if 'down_sign' not in config:
        config.down_sign = -1
            
    if config.capture_env == "locklab":
        ground_truth_cols = ['Fx','Fy','Fz']
    else: ground_truth_cols = ['Fx','Fy','Fz','Cx','Cy']

    # get sample rate from metadata if it exists, otherwise use default
    data_sample_rate = int(raw_data_artifact.metadata['sample_rate']) if 'sample_rate' in raw_data_artifact.metadata else 1000
    down_sign = int(raw_data_artifact.metadata['down_sign']) if 'down_sign' in raw_data_artifact.metadata else -1

    train_set, test_set, validation_set, test_CoP, test_forces = utils.make_datasets(
        data_dir, 
        x_scaler,
        y_scaler,
        device,
        config.capture_env, 
        train_test_val_split=config.train_test_val_split,
        raw_data_artifact=raw_data_artifact, 
        dataset_fraction=config.dataset_fraction,
        ground_truth_columns=ground_truth_cols, 
        physics_informed_type=config.physics_informed_type,
        engineered_features=config.engineered_features,
        targets=config.target_cols,
        val_sample_grouplength=round(data_sample_rate/config.dataset_fraction),
        scale=True,
        fz_threshold=config.fz_threshold, # should frames below the Fz threshold be removed?
        fz_threshold_value=config.fz_threshold_value, # threshold value for force data
        down_sign=down_sign, 
        zero=config.zero_loadcells, # must the loadcell readings be zeroed?
        inject_zero_frames=config.inject_zero_frames,
        zero_frame_percentage=config.zero_frame_percentage,
        files_to_ignore=config.files_to_ignore,
        test_set_files=config.test_set_files,
        sequence_length=config.sequence_length,
        window_size=config.window_size,
        window_stride=config.window_stride,
    )

    if config.use_autoencoder:
        # Initialize the model, loss function, and optimizer
        num_engineered_features = 0
        if config.engineered_features is not None and 'zero_point' in config.engineered_features:
            num_engineered_features += config.num_loadcells
        if config.engineered_features is not None and 'diff' in config.engineered_features:
            num_engineered_features += config.num_loadcells
        autoencoder = Autoencoder(config.num_loadcells+num_engineered_features, config.autoencoder_encoding_dim)
        ae_criterion = nn.MSELoss()
        ae_optimizer = torch.optim.Adam(autoencoder.parameters(), lr=config.autoencoder_learning_rate)

        ae_train_loader = DataLoader(TensorDataset(train_set.x, train_set.x), batch_size=config.batch_size, shuffle=False)
        ae_val_loader = DataLoader(TensorDataset(validation_set.x, validation_set.x), batch_size=config.batch_size, shuffle=False)
        ae_test_loader = DataLoader(TensorDataset(test_set.x, test_set.x), batch_size=config.batch_size, shuffle=False)

        train_autoencoder(autoencoder, ae_criterion, ae_optimizer, ae_train_loader, config.autoencoder_epochs)
        
        train_set = utils.PlateDataSet(get_latent_space(autoencoder, ae_train_loader), train_set.y, device=device)
        validation_set = utils.PlateDataSet(get_latent_space(autoencoder, ae_val_loader), validation_set.y, device=device)
        test_set = utils.PlateDataSet(get_latent_space(autoencoder, ae_test_loader), test_set.y, device=device)
    
    print("Training data after scaling and splitting:")
    print(train_set[0:5])
        
    return train_set, test_set, validation_set, test_CoP, test_forces

# save the data items to avoid re-fetching them during sweeps
train_set, test_set, val_set, test_CoP, test_forces = None, None, None, None, None

def make(config, raw_data_artifact, device):
    global train_set, test_set, val_set, test_CoP, test_forces, x_scaler, y_scaler

    # If the data hasn't been organised, make the data
    if not train_set:
        train_set, test_set, val_set, test_CoP, test_forces = get_data(config, raw_data_artifact, device)

    if device.type == 'cuda':
        generator = torch.Generator(device='cuda')
    else:
        generator = torch.Generator()

    train_loader = DataLoader(train_set, batch_size=config.batch_size, shuffle=True, generator=generator)
    val_loader = DataLoader(val_set, batch_size=config.batch_size, shuffle=True, generator=generator)
    test_loader = DataLoader(test_set, batch_size=config.batch_size, shuffle=True, generator=generator)

    num_engineered_features = 0
    if config.engineered_features is not None and 'zero_point' in config.engineered_features:
        num_engineered_features += config.num_loadcells
    if config.engineered_features is not None and 'diff' in config.engineered_features:
        num_engineered_features += config.num_loadcells
    if config.use_autoencoder:
        in_features = config.autoencoder_encoding_dim
    else:
        if config.type == "PINN":
            in_features = config.num_loadcells + num_engineered_features
        else:
            in_features = config.num_loadcells + num_engineered_features

    print("in_features is {in_features}")

    if config.type == "FEANN":
        # Load pretrained sim model
        basemodel = Model(base=None, layers=config.base_layers, in_features=in_features, out_features=len(config.target_cols), p_input=config.input_dropout, p_hidden=config.hidden_dropout) # set the layer size properly
        print(basemodel)
        try:
            basemodel.load_state_dict(torch.load('./models/' + config.base_model + '.pt', map_location=device))
        except Exception as e:
            print("Couldn't load base model, check config.base_layers and number of input/output features")
            raise e
        print("Base model:")
        print(basemodel)
        # remove base model's output layer
        basemodel.layers = nn.Sequential(*list(basemodel.layers.children())[:-1])
        
        print("Cropped base model:")
        print(basemodel)      
          
    # Make the model
    if sweeping:
        if config.layercount == 0:
            config.layers = []
        elif config.layercount == 1:
            config.layers = [config.layer_1]
        elif config.layercount == 2:
            config.layers=[config.layer_1, config.layer_2]
        elif config.layercount == 3:
            config.layers=[config.layer_1, config.layer_2, config.layer_3]

    if config.residual_learner:
        if config.type == "LSTM":
            linear_model = LinearModel(in_features, len(config.target_cols), config.sequence_length)
        else:
            linear_model = LinearModel(in_features, len(config.target_cols))
        linear_criterion = nn.MSELoss()
        linear_optimizer = torch.optim.SGD(linear_model.parameters(), lr=0.01)
        linear_model = train_linear_model(linear_model, linear_criterion, linear_optimizer, train_loader, 100)
    
    layers_to_freeze = 0 # i.e. retrain / fine tune all layers
    if config.type == "FEANN":
        model = Model(base=basemodel, layers=config.layers, in_features=config.base_layers[-1], out_features=len(config.target_cols), p_input=config.hidden_dropout, p_hidden=config.hidden_dropout, batch_norm=config.batch_norm, config=config).to(device) 

        if not config.fine_tune: 
            print(*list(model.pretrained_model.children()))
            print("")
            print(model.parameters())
            layers_to_freeze = round(len(*list(model.pretrained_model.children())) / 3 * 2) # think 3 children per layer, 2 params per layer? Not sure the story here

        print("Model")
        print(model)
    elif config.type == "PINN":
        model = Model(layers=config.layers, in_features=in_features, out_features=len(config.target_cols), p_input=config.input_dropout, p_hidden=config.hidden_dropout, batch_norm=config.batch_norm, config=config).to(device) # normal 
        # forward_hook_handle = model.layers.register_forward_hook(forward_hook)
    elif config.type == "LSTM":
        model = Model(layers=config.layers, in_features=in_features, out_features=len(config.target_cols), config=config).to(device)
    elif config.type == "RANN":
        linear_model = LinearModel(in_features, len(config.target_cols))
        linear_criterion = nn.MSELoss()
        linear_optimizer = torch.optim.SGD(linear_model.parameters(), lr=0.01)
        linear_model = train_linear_model(linear_model, linear_criterion, linear_optimizer, train_loader, config.linear_model_epochs)

        model = ResidualModel(linear_model, Model(layers=config.layers, in_features=in_features, out_features=len(config.target_cols), p_input=config.input_dropout, p_hidden=config.hidden_dropout, batch_norm=config.batch_norm, config=config)).to(device)
    else:
        model = Model(layers=config.layers, in_features=in_features, out_features=len(config.target_cols), p_input=config.input_dropout, p_hidden=config.hidden_dropout, batch_norm=config.batch_norm, config=config).to(device) 
        
    params_to_update = [] # if feature-extracting
    # Freeze all layers but the output
    if config.residual_learner:
        model = ResidualModel(linear_model, model).to(device)
        for param in model.linear_model.parameters():
            param.requires_grad = False
        for param in model.residual_nn.parameters():
            params_to_update.append(param)
    else:
        for i, param in enumerate(model.parameters()):
            if i < layers_to_freeze: # TODO: check that this freezes layers from the back to front
                param.requires_grad = False # i.e., weights won't be updated (default is True)
            else:    
                params_to_update.append(param) # add layer to ones the optimizer must use
            
    model.to(device)

    # Make the loss and optimizer
    if config.loss == "MSE":
        criterion = utils.LossWrapper(nn.MSELoss())
    elif config.loss == "MAE":
        criterion = utils.LossWrapper(nn.L1Loss()) # nn.SmoothL1Loss()
    else: # custom physics + MSE loss. Only works if zeroed data is used
        if not config.zeroed:
            raise ValueError("Physics loss multiplies inputs to convert to force and only works with zeroed data")
        # [-3090924+4000000,-3077620+4000000,256047,-2955963+4000000, 57156,-3364739+4000000] -> [16.9329, 17.1807,  2.3846, 19.4467,  0.5323, 11.8327]kg with 200kg (2mV/V) + 100kg (1mV/V), 1.2V ref, 12V excitation, 2e24 resolution, 64 & 128 gain.
        EXCITATION = 12
        counts_to_V_conversion = config.daq_ref_v / (config.daq_max_normal_reading * np.array(config.daq_gains))
        v_to_kg_conversion = np.array(config.loadcell_ranges_kg) / (np.array(config.loadcell_sensitivities_vpv) * EXCITATION)
        counts_to_forces = counts_to_V_conversion * v_to_kg_conversion * 9.81
        criterion = utils.PhysicsInformedLoss(counts_to_forces, x_scaler, y_scaler)
    
    print("Model layers:")
    for name,param in model.named_parameters():
        print("\t",name, "- will update:", param.requires_grad)

    # optimizer = torch.optim.Adam(model.parameters(), lr=config.upper_learning_rate) # fine tuning
    optimizer = torch.optim.AdamW(params_to_update, lr=config.upper_learning_rate)#, momentum=0.9)

    steps = len(train_loader)
    scheduler = torch.optim.lr_scheduler.OneCycleLR(optimizer, max_lr=config.upper_learning_rate, epochs=config.epochs, steps_per_epoch=steps)

    return model, train_loader, test_loader, val_loader, test_CoP, test_forces, criterion, optimizer, scheduler

def recall(model_path):
    # Make the data
    train, test, val, test_CoP, test_forces = get_data()
    train_loader = DataLoader(train, batch_size=nn_config.batch_size, shuffle=True)
    val_loader = DataLoader(val, batch_size=nn_config.batch_size, shuffle=True)
    test_loader = DataLoader(test, batch_size=nn_config.batch_size, shuffle=True)

    # load the model from storage
    model = Model().to(device)
    # if gpu: model.cuda()
    model.load_state_dict(torch.load(model_path, map_location=device))

    # Make the loss function
    criterion = nn.MSELoss() 
    
    return model, train_loader, test_loader, val_loader, test_CoP, test_forces, criterion

if (training):
    # Build and train with a wandb sweep
    if sweeping:
        if sweep_id is None:
            # sweep_config = append_config_details(sweep_config)
            sweep_id = wandb.sweep(sweep_config, project="force-plate")
        wandb.agent(sweep_id, training_pipeline, project="force-plate")#, count=20)
    else:
        # Build, train and analyze the model with the pipeline
        model, test_loader, test_CoP, criterion, config = training_pipeline(nn_config)
    
else:
    # Load and test an existing model
    testing_pipeline('/content/Models/' + run_name + '.pt')
    # testing_pipeline('/content/drive/MyDrive/D/Documents/2020/Dissertation/Models/' + run_name + '.pt')

# # Calculate reconstruction error with autoencoder for a new input
# # new_input = x_scaler.transform(np.array([1000000, 1000000, 1000000, 1000000, 1000000, 1000000]).reshape(1, -1)) # concrete, bolted, one foot mid
# # new_input = x_scaler.transform(np.array([5000000, 692745, 255293, 963981, 102806, 772358]).reshape(1, -1)) # concrete, bolted, one foot mid
# # new_input = x_scaler.transform(np.array([5117333.3, 757195.9103, 220451.4372, -806679.3026, 496903.0231, 1705879.891]).reshape(1, -1))
# new_input = x_scaler.transform(np.array([913417, 692745, 255293, 963981, 102806, 772358]).reshape(1, -1)) # concrete, bolted, one foot mid
# new_error = reconstruction_error(model, new_input)
# print("Reconstruction error for new input:", new_error)
# is_similar = new_error < threshold
# print("Is the new input similar to training data?", is_similar)

# # Assessing similarity in latent space
# def encode(model, data):
#     model.eval()
#     with torch.no_grad():
#         data = torch.tensor(data, dtype=torch.float32)
#         latent_representation = model.encoder(data)
#     return latent_representation.numpy()

# latent_representation = encode(model, new_input)
# train_latent_representations = encode(model, train_loader.dataset.x)

# # Optionally, use KDE or another method to estimate density in latent space
# from sklearn.neighbors import KernelDensity

# kde = KernelDensity(kernel='gaussian', bandwidth=0.5).fit(train_latent_representations)
# density = kde.score_samples(latent_representation)
# print("Density of similar examples:", density)

# training = False
# test(model.to(device), test_loader, test_CoP, criterion, config)

import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import NearestNeighbors

# Normalize your data
X_train_normalized = train_loader.dataset.x

# Fit k-NN model
n_neighbors = 5  # Number of neighbors you want to find
knn = NearestNeighbors(n_neighbors=n_neighbors)
knn.fit(X_train_normalized)

# New input example
# new_example_normalized = x_scaler.transform(np.array([5117333.3, 757195.9103, 220451.4372, -806679.3026, 496903.0231, 1705879.891]).reshape(1, -1)) # top left, hard press
new_example_normalized = x_scaler.transform(np.array([913417, 692745, 255293, 963981, 102806, 772358]).reshape(1, -1)) # concrete, bolted, one foot mid
# new_example_normalized = x_scaler.transform(np.array([683000, 1380000, 337000, 407000, -50000, 850000]).reshape(1, -1)) # middle, -27,-30,684

# Find the nearest neighbors
distances, indices = knn.kneighbors(new_example_normalized)

# Get the nearest neighbors
nearest_neighbors = x_scaler.inverse_transform(X_train_normalized[indices[0]])
print(new_example_normalized)

print(f"The {n_neighbors} nearest neighbors to the new example are:")
print(nearest_neighbors)
print(f"The distances to the nearest neighbors are:")
print(distances)

# from sklearn.ensemble import IsolationForest

# # Fit Isolation Forest model
# iso_forest = IsolationForest(contamination=0.25, random_state=42)  # Contamination is the proportion of data which are likely outliers
# iso_forest.fit(X_train_normalized)

# # Predict on new data
# prediction = iso_forest.predict(new_example_normalized)

# is_inlier = prediction[0] == 1
# print("Is the new input similar to training data?", is_inlier)

# import numpy as np
# from sklearn.preprocessing import StandardScaler
# from sklearn.svm import OneClassSVM

# # Fit One-Class SVM model
# ocsvm = OneClassSVM(kernel='rbf', nu=0.3, gamma='scale')#, max_iter=100)
# ocsvm.fit(X_train_normalized[::10])

# # Predict on new data
# prediction = ocsvm.predict(new_example_normalized)

# is_inlier = prediction[0] == 1
# print("Is the new input similar to training data?", is_inlier)

# import matplotlib.pyplot as plt
# import numpy as np
# import plotly.graph_objects as go
# import plotly.offline as py_offline

# cols_to_plot = [0, 5]

# # Determine the range for the grid based on the first two features of the normalized data
# x_min, x_max = X_train_normalized[:, cols_to_plot[0]].min() - 1, X_train_normalized[:, cols_to_plot[0]].max() + 1
# y_min, y_max = X_train_normalized[:, cols_to_plot[1]].min() - 1, X_train_normalized[:, cols_to_plot[1]].max() + 1

# # Create a grid for visualization (2D for first two features)
# xx, yy = np.meshgrid(np.linspace(x_min, x_max, 500), np.linspace(y_min, y_max, 500))

# # Expand grid to full dimensionality with zeros for other features
# grid = np.c_[xx.ravel(), yy.ravel()]
# num_features = X_train_normalized.shape[1]
# grid_full_dim = np.zeros((grid.shape[0], num_features))
# grid_full_dim[:, :len(cols_to_plot)] = grid

# # Normalize the grid
# grid_normalized = grid_full_dim

# # Decision function for One-Class SVM
# zz_svm = ocsvm.decision_function(grid_normalized).reshape(xx.shape)

# # Decision function for Isolation Forest
# zz_iforest = iso_forest.decision_function(grid_normalized).reshape(xx.shape)

# min_zz_svm = zz_svm.min()
# max_zz_svm = zz_svm.max()
# min_zz_iforest = zz_iforest.min()
# max_zz_iforest = zz_iforest.max()

# # Generate contour levels
# levels_svm = np.linspace(min_zz_svm, max_zz_svm, 10)
# levels_iforest = np.linspace(min_zz_iforest, max_zz_iforest, 10)
# levels_iforest = np.unique(np.sort(levels_iforest))

# # Plotly 3D Surface plot for One-Class SVM
# fig_svm = go.Figure(data=[go.Surface(z=zz_svm, x=xx, y=yy, colorscale='Blues')])
# fig_svm.update_layout(title='One-Class SVM Boundary', scene=dict(
#                       xaxis_title='Feature 1',
#                       yaxis_title='Feature 2',
#                       zaxis_title='Decision Function Value'))

# # Plotly 3D Surface plot for Isolation Forest
# fig_iforest = go.Figure(data=[go.Surface(z=zz_iforest, x=xx, y=yy, colorscale='Blues')])
# fig_iforest.update_layout(title='Isolation Forest Boundary', scene=dict(
#                           xaxis_title='Feature 1',
#                           yaxis_title='Feature 2',
#                           zaxis_title='Decision Function Value'))

# # Adding scatter plots for the training data and new example
# training_data_trace = go.Scatter3d(
#     x=X_train_normalized[::100, cols_to_plot[0]],
#     y=X_train_normalized[::100, cols_to_plot[1]],
#     z=np.zeros(X_train_normalized[:, 0].shape),
#     mode='markers',
#     marker=dict(size=4, color='black', line=dict(width=0.5, color='black')),
#     name='Training Data'
# )

# new_example_trace = go.Scatter3d(
#     x=[new_example_normalized[0, cols_to_plot[0]]],
#     y=[new_example_normalized[0, cols_to_plot[1]]],
#     z=[0],
#     mode='markers',
#     marker=dict(size=10, color='red', line=dict(width=1, color='black')),
#     name='New Example'
# )

# # # Add traces to the figures
# fig_svm.add_trace(training_data_trace)
# fig_svm.add_trace(new_example_trace)

# fig_iforest.add_trace(training_data_trace)
# fig_iforest.add_trace(new_example_trace)

# # Show the plots
# py_offline.plot(fig_svm)
# py_offline.plot(fig_iforest)

import pandas as pd
import numpy as np
import plotly.graph_objs as go
import plotly.offline as py_offline
from ipywidgets import interactive, VBox, Dropdown, FloatSlider

# Initialize offline mode
py_offline.init_notebook_mode(connected=True)
use_predefined_ranges = True

# Sample data creation (replace this with your dataframe)
# df = pd.DataFrame({'Fx': np.random.randn(1000) * 100, 'Fy': np.random.randn(1000) * 100, 'Fz': np.random.randn(1000) * 100})

# Make a named dataframe from the training data
df = y_scaler.inverse_transform(train_loader.dataset.y[::100])
df = pd.DataFrame(df, columns=config.target_cols)

# Create a Plotly FigureWidget
fig = go.FigureWidget()

# Initialize the scatter plot with empty data
scatter = fig.add_scatter3d(
    x=[],
    y=[],
    z=[],
    mode='markers',
    marker=dict(size=1)
)

if use_predefined_ranges:
    x_range = [-1000, 1000]
    y_range = [-1000, 1000]
    z_range = [0, 2500]
else:
    x_range = [df['Fx'].min(), df['Fx'].max()]
    y_range = [df['Fy'].min(), df['Fy'].max()]
    z_range = [df['Fz'].min(), df['Fz'].max()]

# Calculate the aspect ratio
x_span = abs(x_range[1] - x_range[0])
y_span = abs(y_range[1] - y_range[0])
z_span = abs(z_range[1] - z_range[0])

# normalise the spans to make an aspect ratio
x_span = x_span / max([x_span, y_span, z_span])
y_span = y_span / max([x_span, y_span, z_span])
z_span = z_span / max([x_span, y_span, z_span])

# Set the aspect ratio to be consistent
# aspect_ratio = dict(x=1, y=1, z=1)
aspect_ratio = dict(x=x_span, y=y_span, z=z_span)

fig.update_layout(
    scene=dict(
        xaxis=dict(title='Fx', range=x_range, autorange=False),
        yaxis=dict(title='Fy', range=y_range, autorange=False),
        zaxis=dict(title='Fz', range=z_range, autorange=False),
        aspectmode='manual',
        aspectratio=aspect_ratio,
    ),
    title='3D Force Vectors',
)

# Define the function to update the plot based on the slice index and axis
def update_plot(slice_start, axis, thickness):
    # Determine the end of the slice
    slice_end = slice_start + thickness
    
    # Filter the dataframe based on the selected axis and slice range
    if axis == 'Fx':
        slice_data = df[(df['Fx'] >= slice_start) & (df['Fx'] < slice_end)]
    elif axis == 'Fy':
        slice_data = df[(df['Fy'] >= slice_start) & (df['Fy'] < slice_end)]
    else:  # axis == 'Fz'
        slice_data = df[(df['Fz'] >= slice_start) & (df['Fz'] < slice_end)]
    
    # Update the scatter plot data
    with fig.batch_update():
        scatter.data[0].update(
            x=slice_data['Fx'],
            y=slice_data['Fy'],
            z=slice_data['Fz']
        )
    
    fig.update_layout(
        scene=dict(
            xaxis=dict(title='Fx', range=x_range, autorange=False),
            yaxis=dict(title='Fy', range=y_range, autorange=False),
            zaxis=dict(title='Fz', range=z_range, autorange=False),
            aspectmode='manual',
            aspectratio=aspect_ratio,
        ),
        title='3D Force Vectors',
    )

# Define the interactive controls
axis_dropdown = Dropdown(options=['Fx', 'Fy', 'Fz'], value='Fx', description='Axis')
thickness_slider = FloatSlider(value=100.0, min=1.0, max=5000.0, step=1.0, description='Thickness')
start_slider = FloatSlider(value=0.0, min=-2000.0, max=2000.0, step=1.0, description='Slice Start')

# Create the interactive widget
interactive_plot = interactive(update_plot, slice_start=start_slider, axis=axis_dropdown, thickness=thickness_slider)

# Display the plot and interactive widget
display(VBox([fig, interactive_plot]))


import pandas as pd
import numpy as np
import plotly.graph_objs as go
import plotly.offline as py_offline
from ipywidgets import interactive, VBox, HBox, SelectionSlider, IntSlider, Layout

# Initialize offline mode
py_offline.init_notebook_mode(connected=True)
use_predefined_ranges = True

# Sample data creation (replace this with your dataframe)
# df = pd.DataFrame({'Fx': np.random.randn(1000) * 100, 'Fy': np.random.randn(1000) * 100, 'Fz': np.random.randn(1000) * 100, 'Cx': np.random.uniform(-150, 150, 1000), 'Cy': np.random.uniform(-250, 250, 1000)})

# Make a named dataframe from the training data
df = y_scaler.inverse_transform(train_loader.dataset.y[::10])
df = pd.DataFrame(df, columns=config.target_cols)

# Define the force plate dimensions
plate_width = 300
plate_height = 500

# Create a Plotly FigureWidget for the 3D force vectors
fig_3d = go.FigureWidget()
scatter_3d = fig_3d.add_scatter3d(
    x=[],
    y=[],
    z=[],
    mode='markers',
    marker=dict(size=1)
)

if use_predefined_ranges:
    x_range = [-1000, 1000]
    y_range = [-1000, 1000]
    z_range = [0, 2500]
else:
    x_range = [df['Fx'].min(), df['Fx'].max()]
    y_range = [df['Fy'].min(), df['Fy'].max()]
    z_range = [df['Fz'].min(), df['Fz'].max()]

# Calculate the aspect ratio
x_span = abs(x_range[1] - x_range[0])
y_span = abs(y_range[1] - y_range[0])
z_span = abs(z_range[1] - z_range[0])

# Normalize the spans to make an aspect ratio
x_span /= max([x_span, y_span, z_span])
y_span /= max([x_span, y_span, z_span])
z_span /= max([x_span, y_span, z_span])

# Set the aspect ratio to be consistent
aspect_ratio = dict(x=x_span, y=y_span, z=z_span)

fig_3d.update_layout(
    scene=dict(
        xaxis=dict(title='Fx', range=x_range, autorange=False),
        yaxis=dict(title='Fy', range=y_range, autorange=False),
        zaxis=dict(title='Fz', range=z_range, autorange=False),
        aspectmode='manual',
        aspectratio=aspect_ratio,
    ),
    title='3D Force Vectors',
)

# Create a Plotly FigureWidget for the 2D force plate
fig_2d = go.FigureWidget()
all_points_2d = fig_2d.add_scatter(
    x=df['Cx'], y=df['Cy'],
    mode='markers',
    marker=dict(size=2),
    name='All Points'
)
selected_points_2d = fig_2d.add_scatter(
    x=[], y=[],
    mode='markers',
    marker=dict(size=2, color='red'),
    name='Selected Points'
)
segment_boundary = fig_2d.add_scatter(
    x=[], y=[],
    mode='lines',
    line=dict(color='red', width=1),
    fill="toself",
    fillcolor='rgba(0, 0, 255, 0.1)',
    name='Segment Boundary'
)

fig_2d.update_layout(
    title='Force Plate Segmentation',
    xaxis=dict(
        title='Cx (mm)',
        range=[-plate_width / 2, plate_width / 2],
        autorange=False,
        scaleanchor="y",  # Ensure the x-axis is scaled according to the y-axis
        scaleratio=1
    ),
    yaxis=dict(
        title='Cy (mm)',
        range=[-plate_height / 2, plate_height / 2],
        autorange=False
    ),
    showlegend=False,
    xaxis_range=[-plate_width / 2, plate_width / 2],
    yaxis_range=[-plate_height / 2, plate_height / 2],
)

fig_2d.update_yaxes(range=[-plate_height / 2, plate_height / 2])
# fig_2d.update_xaxes(range=[-plate_width / 2, plate_width / 2])

# Define the function to update the 3D plot based on the selected segment
def update_plot(x_seg, y_seg, seg_size):
    # Calculate segment boundaries
    x_start = x_seg * seg_size - plate_width / 2
    x_end = x_start + seg_size
    y_start = y_seg * seg_size - plate_height / 2
    y_end = y_start + seg_size
    
    # Filter the dataframe based on the selected segment
    slice_data = df[(df['Cx'] >= x_start) & (df['Cx'] < x_end) & (df['Cy'] >= y_start) & (df['Cy'] < y_end)]
    
    # Update the scatter plot data
    with fig_3d.batch_update():
        scatter_3d.data[0].x = slice_data['Fx']
        scatter_3d.data[0].y = slice_data['Fy']
        scatter_3d.data[0].z = slice_data['Fz']
    
    fig_3d.update_layout(
        scene=dict(
            xaxis=dict(title='Fx', range=x_range, autorange=False),
            yaxis=dict(title='Fy', range=y_range, autorange=False),
            zaxis=dict(title='Fz', range=z_range, autorange=False),
            aspectmode='manual',
            aspectratio=aspect_ratio,
        ),
        title=f'Segment X: ({x_start:.1f},{x_end:.1f}), Y: ({y_start:.1f},{y_end:.1f})',
    )

    # Update the 2D plot to highlight the selected segment
    with fig_2d.batch_update():
        fig_2d.data[1].x = slice_data['Cx']
        fig_2d.data[1].y = slice_data['Cy']
        fig_2d.data[2].x = [x_start, x_end, x_end, x_start, x_start]
        fig_2d.data[2].y = [y_start, y_start, y_end, y_end, y_start]

    fig_2d.update_yaxes(range=[-plate_height / 2, plate_height / 2])
    # fig_2d.update_xaxes(range=[-plate_width / 2, plate_width / 2])

# Define the interactive controls
seg_size_slider = IntSlider(value=50, min=10, max=100, step=10, description='Segment Size (mm)')

# Generate segment labels based on the segment size
def generate_segment_labels(seg_size):
    x_segments = [(i * seg_size - plate_width / 2, (i + 1) * seg_size - plate_width / 2) for i in range(int(plate_width / seg_size))]
    y_segments = [(i * seg_size - plate_height / 2, (i + 1) * seg_size - plate_height / 2) for i in range(int(plate_height / seg_size))]
    x_labels = [f"({x[0]:.1f},{x[1]:.1f})" for x in x_segments]
    y_labels = [f"({y[0]:.1f},{y[1]:.1f})" for y in y_segments]
    return x_labels, y_labels

x_labels, y_labels = generate_segment_labels(seg_size_slider.value)

x_seg_slider = SelectionSlider(options=x_labels, description='X Segment')
y_seg_slider = SelectionSlider(options=y_labels, description='Y Segment')

def update_segment_sliders(*args):
    x_labels, y_labels = generate_segment_labels(seg_size_slider.value)
    x_seg_slider.options = x_labels
    y_seg_slider.options = y_labels
    x_seg_slider.value = x_labels[0]
    y_seg_slider.value = y_labels[0]

seg_size_slider.observe(update_segment_sliders, 'value')

# Update the plot when the sliders change
def update_plot_wrapper(x_label, y_label, seg_size):
    x_labels, y_labels = generate_segment_labels(seg_size)
    x_seg = x_labels.index(x_label)
    y_seg = y_labels.index(y_label)
    update_plot(x_seg, y_seg, seg_size)

# Create the interactive widget
interactive_plot = interactive(update_plot_wrapper, x_label=x_seg_slider, y_label=y_seg_slider, seg_size=seg_size_slider)

# Display the plots and interactive widget side by side
# display(HBox([fig_2d, fig_3d]))
# display(interactive_plot)

display(HBox([VBox([fig_2d, interactive_plot], layout=Layout(width='350px')), fig_3d]))


