#!/usr/bin/env python3
"""
Example showing how to use the updated make_datasets function with zero frame injection.
"""

from sklearn.preprocessing import StandardScaler
import torch

# Example of how to use the updated make_datasets function with zero frame injection

def example_usage():
    """
    Example showing how to call make_datasets with zero frame injection enabled.
    """
    
    # Initialize scalers
    x_scaler = StandardScaler()
    y_scaler = StandardScaler()
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Example configuration - you would replace these with your actual values
    directory = "path/to/your/data"  # Replace with actual data directory
    
    # Call make_datasets with zero frame injection enabled (default)
    train_set, test_set, validation_set, test_CoP, test_forces = make_datasets(
        directory=directory,
        x_scaler=x_scaler,
        y_scaler=y_scaler,
        device=device,
        set_type="rig",  # or "sim" or "locklab"
        
        # Zero frame injection parameters (NEW!)
        inject_zero_frames=True,  # Enable zero frame injection (default: True)
        zero_frame_percentage=0.05,  # 5% of total data will be zero frames (default: 0.05)
        
        # Other existing parameters
        train_test_val_split=[0.8, 0.1, 0.1],
        ground_truth_columns=['Fx', 'Fy', 'Fz', 'Cy', 'Cx'],
        targets=['Fx', 'Fy', 'Fz'],
        scale=True,
        fz_threshold=True,
        fz_threshold_value=10,
        down_sign=1,
        zero=True,  # Enable zeroing of loadcell readings
        files_to_ignore=[],
        test_set_files=[],
        sequence_length=None,
    )
    
    print("Datasets created successfully with zero frame injection!")
    print(f"Training set size: {len(train_set)}")
    print(f"Test set size: {len(test_set)}")
    print(f"Validation set size: {len(validation_set)}")
    
    return train_set, test_set, validation_set, test_CoP, test_forces

def example_without_zero_frames():
    """
    Example showing how to disable zero frame injection.
    """
    
    # Initialize scalers
    x_scaler = StandardScaler()
    y_scaler = StandardScaler()
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Example configuration
    directory = "path/to/your/data"  # Replace with actual data directory
    
    # Call make_datasets with zero frame injection disabled
    train_set, test_set, validation_set, test_CoP, test_forces = make_datasets(
        directory=directory,
        x_scaler=x_scaler,
        y_scaler=y_scaler,
        device=device,
        set_type="rig",
        
        # Disable zero frame injection
        inject_zero_frames=False,  # Disable zero frame injection
        
        # Other parameters remain the same
        train_test_val_split=[0.8, 0.1, 0.1],
        ground_truth_columns=['Fx', 'Fy', 'Fz', 'Cy', 'Cx'],
        targets=['Fx', 'Fy', 'Fz'],
        scale=True,
        fz_threshold=True,
        fz_threshold_value=10,
        down_sign=1,
        zero=True,
        files_to_ignore=[],
        test_set_files=[],
        sequence_length=None,
    )
    
    print("Datasets created without zero frame injection!")
    return train_set, test_set, validation_set, test_CoP, test_forces

def example_custom_zero_percentage():
    """
    Example showing how to use a custom zero frame percentage.
    """
    
    # Initialize scalers
    x_scaler = StandardScaler()
    y_scaler = StandardScaler()
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Example configuration
    directory = "path/to/your/data"  # Replace with actual data directory
    
    # Call make_datasets with custom zero frame percentage
    train_set, test_set, validation_set, test_CoP, test_forces = make_datasets(
        directory=directory,
        x_scaler=x_scaler,
        y_scaler=y_scaler,
        device=device,
        set_type="rig",
        
        # Custom zero frame injection settings
        inject_zero_frames=True,
        zero_frame_percentage=0.10,  # 10% of total data will be zero frames
        
        # Other parameters
        train_test_val_split=[0.8, 0.1, 0.1],
        ground_truth_columns=['Fx', 'Fy', 'Fz', 'Cy', 'Cx'],
        targets=['Fx', 'Fy', 'Fz'],
        scale=True,
        fz_threshold=True,
        fz_threshold_value=10,
        down_sign=1,
        zero=True,
        files_to_ignore=[],
        test_set_files=[],
        sequence_length=None,
    )
    
    print("Datasets created with 10% zero frames!")
    return train_set, test_set, validation_set, test_CoP, test_forces

if __name__ == "__main__":
    print("Zero Frame Injection Examples")
    print("=" * 40)
    print()
    
    print("This file shows examples of how to use the updated make_datasets function")
    print("with the new zero frame injection feature.")
    print()
    
    print("Key new parameters:")
    print("- inject_zero_frames: bool (default: True) - Enable/disable zero frame injection")
    print("- zero_frame_percentage: float (default: 0.05) - Percentage of total data that should be zero frames")
    print()
    
    print("The zero frames are injected right after the zeroing step in data preparation,")
    print("ensuring that 5% (or your specified percentage) of the total dataset consists")
    print("of frames where all loadcell readings and target values are 0.")
    print()
    
    print("This helps the model learn to handle zero-force conditions properly.")
